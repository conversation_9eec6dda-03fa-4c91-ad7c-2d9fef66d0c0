import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/molecules/dialoges.dart';
import 'package:melodyze/core/ui/molecules/navigation/melo_scaffold.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/modules/profile/bloc/profile_bloc.dart';
import 'package:melodyze/modules/profile/bloc/profile_event.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
import 'package:melodyze/modules/video_player/video_player_bloc/video_player_bloc.dart';
import 'package:melodyze/modules/video_player/video_player_full_screen.dart';
import 'package:visibility_detector/visibility_detector.dart';

@RoutePage()
class VideoPlayerReelScreen extends StatelessWidget {
  final List<RecordingModel> recordings;
  final bool showMenubar;
  final bool isPublishedRecording;
  final bool showAppBar;
  final bool disableVideo;
  final bool showPersonalizationButton;
  final bool isReelScreen;
  final VideoPlayerBloc videoPlayerBloc;
  final bool autoPlay;

  VideoPlayerReelScreen({
    super.key,
    required this.recordings,
    this.showMenubar = false,
    this.isPublishedRecording = false,
    this.showAppBar = true,
    this.disableVideo = false,
    this.showPersonalizationButton = false,
    this.isReelScreen = false,
    this.autoPlay = true,
  }) : videoPlayerBloc = VideoPlayerBloc(isReel: isReelScreen);

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: Key('video_player_reel_screen'),
      onVisibilityChanged: (info) {
        if (info.visibleFraction == 0) {
          videoPlayerBloc.add(VideoPauseEvent());
        }
      },
      child: MeloScaffold(
        showAppBar: showAppBar,
        showBackground: false,
        body: Padding(
          padding: EdgeInsets.only(top: 58.0, bottom: 108.0),
          child: BlocProvider(
            create: (context) {
              final videoFilePath = recordings.first.finalVideoFilePath.isNotEmpty ? recordings.first.finalVideoFilePath : recordings.first.finalMixedAudioPath;
              return videoPlayerBloc
                ..add(LoadVideoEvent(
                  videoFilePath: videoFilePath,
                  videoType: VideoType.url,
                  autoPlay: autoPlay,
                  loop: false,
                ));
            },
            child: PageView.builder(
              scrollDirection: Axis.vertical,
              itemCount: recordings.length,
              controller: videoPlayerBloc.pageController,
              onPageChanged: (value) {
                final recording = recordings[value];
                videoPlayerBloc.add(LoadVideoEvent(
                  videoFilePath: recording.finalVideoFilePath.isNotEmpty ? recording.finalVideoFilePath : recording.finalMixedAudioPath,
                  videoType: VideoType.url,
                  autoPlay: true,
                  loop: false,
                ));
              },
              itemBuilder: (context, index) {
                final recording = recordings[index];
                return VideoPlayerFullScreen(
                  key: Key(recording.id),
                  disableVideo: disableVideo || recording.finalVideoFilePath.isEmpty,
                  recordingModel: recording,
                  showPersonalizationButton: showPersonalizationButton,
                  isReelScreen: isReelScreen,
                );
              },
            ),
          ),
        ),
        secondaryAction: showMenubar
            ? (context) {
                return PopupMenuButton<String>(
                  icon: ShaderMask(
                    shaderCallback: (bounds) => AppGradients.gradientPinkIcon.createShader(bounds),
                    child: const Icon(Icons.more_vert, size: 28, color: Colors.white),
                  ),
                  color: Colors.transparent,
                  elevation: 0,
                  itemBuilder: (BuildContext context) => [
                    PopupMenuItem(
                      padding: EdgeInsets.zero,
                      child: AppGradientContainer(
                        gradient: AppGradients.gradientBlackTeal,
                        child: Column(
                          children: [
                            PopupMenuItem(
                              value: isPublishedRecording ? 'move_to_draft' : 'make_it_final',
                              child: ListTile(
                                leading: const Icon(
                                  Icons.check,
                                  color: AppColors.white,
                                ),
                                title: Text(
                                  isPublishedRecording ? 'Move to draft' : 'Make it published',
                                  style: AppTextStyles.text16medium.copyWith(
                                    fontFamily: AppFonts.iceland,
                                    color: AppColors.white,
                                  ),
                                ),
                              ),
                              onTap: () async {
                                final result = await showYesNoDialog(context: context, title: isPublishedRecording ? 'Move to draft' : 'Make it published');
                                if (result && context.mounted) {
                                  DI().resolve<ProfileBloc>().add(ProfileTogglePublishEvent(
                                        recording: recordings.first,
                                        publish: !isPublishedRecording,
                                      ));
                                }
                              },
                            ),
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 16.0),
                              child: ImageLoader.fromAsset(AssetPaths.gradientdivider),
                            ),
                            PopupMenuItem(
                              value: 'delete',
                              child: ListTile(
                                leading: Icon(
                                  Icons.delete_forever,
                                  color: AppColors.white,
                                ),
                                title: Text(
                                  'Delete',
                                  style: AppTextStyles.text16medium.copyWith(
                                    fontFamily: AppFonts.iceland,
                                    color: AppColors.white,
                                  ),
                                ),
                                onTap: () async {
                                  final result = await showYesNoDialog(context: context, title: 'Delete recording');
                                  if (result && context.mounted) {
                                    videoPlayerBloc.add(VideoStopEvent());
                                    Navigator.pop(context);
                                    await Future.delayed(const Duration(milliseconds: 100));
                                    if (context.mounted) {
                                      DI().resolve<ProfileBloc>().add(DeleteRecordingEvent(recordings.first.id));
                                      context.router.pop();
                                    }
                                  }
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              }
            : null,
      ),
    );
  }
}
